/**
 * 進度追蹤 Composable
 * 提供實時進度更新、輪詢管理和狀態追蹤功能
 */

import { ref, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { parseAPI, handleApiError } from '../services/api'

export interface ProgressState {
  taskId: string | null
  status: string
  progress: number
  currentStep: string
  estimatedTime?: number
  startTime?: string
  errorMessage?: string
  steps: ProgressStep[]
  stats?: Record<string, ProgressStat>
}

export interface ProgressStep {
  title: string
  description?: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  timestamp?: string
  details?: Record<string, any>
}

export interface ProgressStat {
  label: string
  value: string | number
}

export interface ProgressOptions {
  pollingInterval?: number
  autoStart?: boolean
  showNotifications?: boolean
  onStatusChange?: (status: string) => void
  onProgress?: (progress: number) => void
  onCompleted?: (result: any) => void
  onFailed?: (error: string) => void
}

export function useProgressTracking(options: ProgressOptions = {}) {
  const {
    pollingInterval = 2000,
    autoStart = true,
    showNotifications = true,
    onStatusChange,
    onProgress,
    onCompleted,
    onFailed
  } = options

  // 響應式狀態
  const state = ref<ProgressState>({
    taskId: null,
    status: 'pending',
    progress: 0,
    currentStep: '等待中...',
    estimatedTime: undefined,
    startTime: undefined,
    errorMessage: undefined,
    steps: [],
    stats: undefined
  })

  const isPolling = ref(false)
  const pollingTimer = ref<NodeJS.Timeout | null>(null)
  const isRefreshing = ref(false)

  // 計算屬性
  const isProcessing = computed(() => {
    return ['pending', 'processing'].includes(state.value.status)
  })

  const isCompleted = computed(() => {
    return state.value.status === 'completed'
  })

  const isFailed = computed(() => {
    return state.value.status === 'failed'
  })

  const canCancel = computed(() => {
    return isProcessing.value && state.value.taskId
  })

  // 方法
  const startTracking = async (taskId: string) => {
    console.log('🚀 startTracking 開始:', { taskId, autoStart })
    state.value.taskId = taskId

    // 總是載入任務狀態，不管 autoStart 設置
    await loadTaskStatus()

    console.log('📋 startTracking 載入狀態後:', {
      status: state.value.status,
      isProcessing: isProcessing.value,
      hasOnCompleted: !!onCompleted
    })

    // 如果任務已經完成，觸發完成回調
    if (state.value.status === 'completed' && onCompleted) {
      console.log('✅ startTracking 觸發完成回調')
      onCompleted(state.value)
    }

    if (autoStart && isProcessing.value) {
      console.log('🔄 startTracking 開始輪詢')
      startPolling()
    }
  }

  const loadTaskStatus = async () => {
    if (!state.value.taskId) return

    try {
      isRefreshing.value = true
      const response = await parseAPI.getParseStatus(state.value.taskId)
      const taskInfo = response.data

      console.log('🔍 useProgressTracking loadTaskStatus:', {
        taskId: state.value.taskId,
        oldStatus: state.value.status,
        newStatus: taskInfo.status,
        oldProgress: state.value.progress,
        newProgress: taskInfo.progress,
        taskInfo
      })

      // 更新狀態
      const oldStatus = state.value.status
      const oldProgress = state.value.progress

      state.value.status = taskInfo.status
      state.value.progress = taskInfo.progress || 0
      state.value.currentStep = taskInfo.current_step || '處理中...'
      state.value.estimatedTime = taskInfo.estimated_time_remaining
      state.value.startTime = taskInfo.started_at
      state.value.errorMessage = taskInfo.error_message

      // 更新步驟
      updateSteps(taskInfo)

      // 更新統計
      updateStats(taskInfo)

      // 觸發回調
      if (oldStatus !== state.value.status && onStatusChange) {
        onStatusChange(state.value.status)
      }

      if (oldProgress !== state.value.progress && onProgress) {
        onProgress(state.value.progress)
      }

      // 處理狀態變化
      if (state.value.status === 'completed' && oldStatus !== 'completed') {
        console.log('🎉 任務完成狀態變化觸發:', { oldStatus, newStatus: state.value.status })
        stopPolling()
        if (showNotifications) {
          ElMessage.success('處理完成！')
        }
        if (onCompleted) {
          console.log('🔄 調用 onCompleted 回調')
          onCompleted(taskInfo)
        }
      } else if (state.value.status === 'failed' && oldStatus !== 'failed') {
        console.log('❌ 任務失敗狀態變化觸發:', { oldStatus, newStatus: state.value.status })
        stopPolling()
        if (showNotifications) {
          ElMessage.error(`處理失敗: ${state.value.errorMessage || '未知錯誤'}`)
        }
        if (onFailed) {
          onFailed(state.value.errorMessage || '未知錯誤')
        }
      } else {
        console.log('📊 狀態更新但無變化觸發:', { oldStatus, newStatus: state.value.status })
      }

    } catch (error) {
      const apiError = handleApiError(error)
      console.error('載入任務狀態失敗:', apiError)

      if (showNotifications) {
        ElMessage.error(`載入狀態失敗: ${apiError.message}`)
      }
    } finally {
      isRefreshing.value = false
    }
  }

  const startPolling = () => {
    if (pollingTimer.value) return

    isPolling.value = true
    pollingTimer.value = setInterval(async () => {
      if (isProcessing.value) {
        await loadTaskStatus()
      } else {
        stopPolling()
      }
    }, pollingInterval)
  }

  const stopPolling = () => {
    if (pollingTimer.value) {
      clearInterval(pollingTimer.value)
      pollingTimer.value = null
    }
    isPolling.value = false
  }

  const refreshStatus = async () => {
    await loadTaskStatus()
  }

  const cancelTask = async () => {
    if (!state.value.taskId) return

    try {
      await parseAPI.cancelParse(state.value.taskId)
      state.value.status = 'cancelled'
      state.value.currentStep = '已取消'
      stopPolling()

      if (showNotifications) {
        ElMessage.info('任務已取消')
      }
    } catch (error) {
      const apiError = handleApiError(error)
      if (showNotifications) {
        ElMessage.error(`取消失敗: ${apiError.message}`)
      }
    }
  }

  const updateSteps = (taskInfo: any) => {
    // 根據任務信息更新步驟
    const steps: ProgressStep[] = []

    // 基本步驟
    steps.push({
      title: '任務創建',
      status: 'completed',
      timestamp: taskInfo.created_at,
      description: '解析任務已創建並加入佇列'
    })

    if (taskInfo.started_at) {
      steps.push({
        title: '開始處理',
        status: 'completed',
        timestamp: taskInfo.started_at,
        description: '開始解析PDF文件'
      })
    }

    if (taskInfo.status === 'processing') {
      steps.push({
        title: '正在處理',
        status: 'processing',
        timestamp: taskInfo.updated_at,
        description: taskInfo.current_step,
        details: {
          '進度': `${taskInfo.progress || 0}%`,
          '方法': taskInfo.parse_method
        }
      })
    }

    if (taskInfo.status === 'completed') {
      steps.push({
        title: '處理完成',
        status: 'completed',
        timestamp: taskInfo.completed_at,
        description: 'PDF解析已完成'
      })
    }

    if (taskInfo.status === 'failed') {
      steps.push({
        title: '處理失敗',
        status: 'failed',
        timestamp: taskInfo.updated_at,
        description: taskInfo.error_message || '處理過程中發生錯誤'
      })
    }

    state.value.steps = steps
  }

  const updateStats = (taskInfo: any) => {
    // 更新統計信息
    const stats: Record<string, ProgressStat> = {}

    if (taskInfo.progress !== undefined) {
      stats.progress = {
        label: '完成進度',
        value: `${taskInfo.progress}%`
      }
    }

    if (taskInfo.estimated_time_remaining) {
      stats.remaining = {
        label: '預計剩餘',
        value: formatTime(taskInfo.estimated_time_remaining)
      }
    }

    if (taskInfo.started_at) {
      const elapsed = Math.floor((new Date().getTime() - new Date(taskInfo.started_at).getTime()) / 1000)
      stats.elapsed = {
        label: '已用時間',
        value: formatTime(elapsed)
      }
    }

    stats.method = {
      label: '解析方法',
      value: getMethodName(taskInfo.parse_method)
    }

    state.value.stats = stats
  }

  const formatTime = (seconds: number): string => {
    if (!seconds || seconds <= 0) return '未知'

    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)

    if (hours > 0) {
      return `${hours}h${minutes}m`
    } else if (minutes > 0) {
      return `${minutes}m${secs}s`
    } else {
      return `${secs}s`
    }
  }

  const getMethodName = (method: string): string => {
    const methodMap = {
      text: '文字解析',
      ocr: 'OCR解析',
      multimodal: 'AI多模態解析'
    }
    return methodMap[method] || method
  }

  // 清理
  onUnmounted(() => {
    stopPolling()
  })

  return {
    // 狀態
    state: computed(() => state.value),
    isPolling: computed(() => isPolling.value),
    isRefreshing: computed(() => isRefreshing.value),
    isProcessing,
    isCompleted,
    isFailed,
    canCancel,

    // 方法
    startTracking,
    loadTaskStatus,
    startPolling,
    stopPolling,
    refreshStatus,
    cancelTask
  }
}
