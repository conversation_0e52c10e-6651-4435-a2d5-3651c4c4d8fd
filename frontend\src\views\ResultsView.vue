<template>
  <div class="results-container">
    <!-- 任務狀態卡片 -->
    <TaskStatusCard
      v-if="taskInfo"
      :task-info="taskInfo"
      :show-start-option="!route.params.taskId"
      :is-starting="isStartingParse"
      v-model:selected-method="selectedParseMethod"
      @retry="handleRetry"
      @go-back="handleGoBack"
      @start-parse="handleStartParse"
    />

    <!-- 增強進度追蹤 -->
    <ProgressTracker
      v-if="progressState.taskId && isProgressProcessing && !hasResult"
      :title="`解析進度 - ${progressState.taskId}`"
      :progress="progressState.progress"
      :status="progressState.status"
      :current-step="progressState.currentStep"
      :estimated-time="progressState.estimatedTime"
      :start-time="progressState.startTime"
      :error-message="progressState.errorMessage"
      :steps="progressState.steps"
      :stats="progressState.stats"
      :can-cancel="canCancel"
      :can-refresh="true"
      :is-refreshing="isRefreshing"
      @cancel="handleCancelTask"
      @refresh="handleRefreshProgress"
    />

    <!-- 解析結果展示 -->
    <ParseResultDisplay
      v-if="hasResult || (taskInfo && taskInfo.status === 'completed')"
      :parseResult="parseResult"
      @save-to-knowledge="saveToKnowledge"
      @start-training="startTraining"
      @export="handleExport"
    />

    <!-- 空狀態 -->
    <div v-if="!taskInfo && !isLoading" class="empty-container">
      <el-empty description="沒有找到解析任務">
        <el-button type="primary" @click="handleGoBack">
          返回上傳頁面
        </el-button>
      </el-empty>
    </div>

    <!-- 載入中 -->
    <div v-if="isLoading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { parseAPI, purchaseAPI, handleApiError } from '../services/api'
import { useProgressTracking } from '../composables/useProgressTracking'
import TaskStatusCard from '../components/TaskStatusCard.vue'
import ParseResultDisplay from '../components/ParseResultDisplay.vue'
import ProgressTracker from '../components/ProgressTracker.vue'

const route = useRoute()
const router = useRouter()

// 進度追蹤
const {
  state: progressState,
  isPolling,
  isRefreshing,
  isProcessing: isProgressProcessing,
  isCompleted: isProgressCompleted,
  canCancel,
  startTracking,
  refreshStatus,
  cancelTask
} = useProgressTracking({
  pollingInterval: 2000,
  autoStart: false,
  showNotifications: true,
  onCompleted: async (result) => {
    console.log('🎯 onCompleted 回調被觸發:', result)
    await loadParseResult()
  },
  onFailed: (error) => {
    console.error('解析失敗:', error)
  }
})

// 響應式數據
const taskInfo = ref(null)
const parseResult = ref(null)
const isLoading = ref(true)
const pollingTimer = ref(null)
const selectedParseMethod = ref('text')
const isStartingParse = ref(false)

// 計算屬性
const isProcessing = computed(() => {
  return taskInfo.value && ['pending', 'processing'].includes(taskInfo.value.status)
})

const canRetry = computed(() => {
  return taskInfo.value && ['failed', 'cancelled'].includes(taskInfo.value.status)
})

const hasResult = computed(() => {
  // 檢查是否有解析結果數據
  const hasData = !!parseResult.value
  const isCompleted = parseResult.value?.status === 'completed'
  const hasTextContent = !!(parseResult.value?.text_content && parseResult.value.text_content.trim().length > 0)
  const isSuccess = parseResult.value?.success === true

  // 只要有數據且成功，或者有文字內容就顯示結果
  const result = hasData && (isSuccess || hasTextContent) && isCompleted

  console.log('🔍 hasResult 計算:', {
    hasParseResult: hasData,
    status: parseResult.value?.status,
    success: parseResult.value?.success,
    hasTextContent,
    textContentLength: parseResult.value?.text_content?.length || 0,
    result,
    parseResultKeys: parseResult.value ? Object.keys(parseResult.value) : []
  })
  return result
})

// 方法
const loadTaskInfo = async () => {
  const taskId = route.params.taskId
  const fileId = route.query.file_id
  const purchaseId = route.query.purchase_id

  console.log('🔍 ResultsView loadTaskInfo:', {
    taskId,
    fileId,
    purchaseId,
    routeParams: route.params,
    routeQuery: route.query,
    fullPath: route.fullPath
  })

  // 如果有任務ID，直接載入任務信息
  if (taskId) {
    try {
      console.log('📋 開始載入任務狀態:', taskId)
      const response = await parseAPI.getParseStatus(taskId)
      taskInfo.value = response.data

      console.log('📋 任務狀態載入成功:', {
        status: taskInfo.value.status,
        progress: taskInfo.value.progress,
        current_step: taskInfo.value.current_step,
        taskInfo: taskInfo.value
      })

      // 如果任務完成，載入結果
      if (taskInfo.value.status === 'completed') {
        console.log('✅ 任務已完成，開始載入解析結果...')
        await loadParseResult()

        // 強制確保結果被載入
        if (!parseResult.value) {
          console.log('⚠️ 解析結果為空，重試載入...')
          setTimeout(async () => {
            await loadParseResult()
          }, 1000)
        }
      } else {
        console.log('⏳ 任務未完成，當前狀態:', taskInfo.value.status)
      }
    } catch (error) {
      const apiError = handleApiError(error)
      console.error('❌ 載入任務信息失敗:', apiError)

      // 如果API調用失敗，嘗試直接載入解析結果
      console.log('🔄 API調用失敗，嘗試直接載入解析結果...')
      try {
        await loadParseResult()

        // 如果成功載入了結果，創建一個模擬的taskInfo
        if (parseResult.value && parseResult.value.status === 'completed') {
          taskInfo.value = {
            task_id: taskId,
            status: 'completed',
            progress: 100,
            current_step: '解析完成',
            parse_method: 'text'
          }
          console.log('✅ 通過結果API成功載入任務信息')
        } else {
          // 設置錯誤狀態但仍然創建taskInfo以便組件渲染
          taskInfo.value = {
            task_id: taskId,
            status: 'error',
            error: apiError.message,
            progress: 0,
            current_step: '載入失敗'
          }
          ElMessage.error(`載入任務信息失敗: ${apiError.message}`)
        }
      } catch (resultError) {
        console.error('載入解析結果也失敗:', resultError)
        // 即使失敗也要創建taskInfo以便組件渲染
        taskInfo.value = {
          task_id: taskId,
          status: 'error',
          error: `API調用失敗: ${apiError.message}`,
          progress: 0,
          current_step: '載入失敗'
        }
        ElMessage.error(`載入任務信息失敗: ${apiError.message}`)
      }
    }
  }
  // 如果有購案ID，載入購案信息並顯示相關文件
  else if (purchaseId) {
    try {
      console.log('🔍 載入購案信息:', purchaseId)

      // 獲取購案詳情
      const purchaseResponse = await purchaseAPI.getPurchase(purchaseId)
      const purchase = purchaseResponse.data

      console.log('📋 購案詳情:', purchase)

      // 獲取購案關聯的文件和任務
      let firstFileId = null
      let completedTaskId = null
      let taskStatus = 'ready'
      let taskProgress = purchase.progress || 0
      let currentStep = '等待開始解析'

      try {
        const filesResponse = await purchaseAPI.getPurchaseFiles(purchaseId)
        const files = filesResponse.data.files
        console.log('📁 購案文件:', files)

        if (files && files.length > 0) {
          // 使用第一個文件的ID
          firstFileId = files[0].file_id
          console.log('📄 使用文件ID:', firstFileId)

          // 檢查是否有已完成的解析任務
          try {
            const tasksResponse = await parseAPI.getTasksByFileId(firstFileId)
            const tasks = tasksResponse.data.tasks || []
            console.log('📋 文件相關任務:', tasks)

            // 尋找已完成的任務
            const completedTask = tasks.find(task => task.status === 'completed')
            if (completedTask) {
              completedTaskId = completedTask.task_id
              taskStatus = 'completed'
              taskProgress = 100
              currentStep = '解析完成'
              console.log('✅ 找到已完成的任務:', completedTaskId)

              // 如果有已完成的任務，直接載入解析結果
              try {
                const resultResponse = await parseAPI.getParseResult(completedTaskId)
                parseResult.value = resultResponse.data
                console.log('📄 載入解析結果成功:', parseResult.value)
              } catch (resultError) {
                console.warn('載入解析結果失敗:', resultError)
              }
            } else {
              // 檢查是否有進行中的任務
              const processingTask = tasks.find(task => ['pending', 'processing'].includes(task.status))
              if (processingTask) {
                completedTaskId = processingTask.task_id
                taskStatus = processingTask.status
                taskProgress = processingTask.progress || 0
                currentStep = processingTask.current_step || '處理中...'
                console.log('🔄 找到進行中的任務:', completedTaskId)
              }
            }
          } catch (tasksError) {
            console.warn('獲取任務列表失敗:', tasksError)
          }
        }
      } catch (filesError) {
        console.warn('獲取購案文件失敗:', filesError)
      }

      // 創建任務信息用於顯示購案信息
      taskInfo.value = {
        task_id: completedTaskId,
        file_id: firstFileId,
        purchase_id: purchaseId,
        purchase_info: purchase,
        status: taskStatus,
        parse_method: 'text',
        progress: taskProgress,
        current_step: currentStep,
        created_at: purchase.upload_time,
        updated_at: purchase.updated_time || purchase.upload_time
      }

      if (firstFileId) {
        if (taskStatus === 'completed') {
          ElMessage.success('購案信息和解析結果載入成功')
        } else {
          ElMessage.success('購案信息和文件載入成功')
        }
      } else {
        ElMessage.warning('購案信息載入成功，但未找到關聯文件')
      }
    } catch (error) {
      const apiError = handleApiError(error)
      ElMessage.error(`載入購案信息失敗: ${apiError.message}`)
      console.error('載入購案失敗:', error)
    }
  }
  // 如果只有文件ID，顯示文件信息並提供開始解析的選項
  else if (fileId) {
    try {
      // 創建一個模擬的任務信息用於顯示
      taskInfo.value = {
        task_id: null,
        file_id: fileId,
        status: 'ready',
        parse_method: 'text',
        progress: 0,
        current_step: '等待開始解析',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    } catch (error) {
      const apiError = handleApiError(error)
      ElMessage.error(`載入文件信息失敗: ${apiError.message}`)
    }
  }
  else {
    ElMessage.error('缺少任務ID、文件ID或購案ID')
    return
  }
}

const loadParseResult = async () => {
  const taskId = route.params.taskId
  if (!taskId) return

  console.log('📄 loadParseResult 開始載入:', taskId)

  try {
    console.log('🔄 發送API請求...')
    const response = await parseAPI.getParseResult(taskId)
    console.log('📦 API響應:', {
      status: response.status,
      statusText: response.statusText,
      hasData: !!response.data,
      dataType: typeof response.data,
      dataKeys: response.data ? Object.keys(response.data) : [],
      dataSize: response.data ? JSON.stringify(response.data).length : 0
    })

    parseResult.value = response.data
    console.log('✅ loadParseResult 載入成功:', {
      hasResult: !!parseResult.value,
      resultKeys: parseResult.value ? Object.keys(parseResult.value) : [],
      taskId: parseResult.value?.task_id,
      status: parseResult.value?.status
    })
  } catch (error) {
    const apiError = handleApiError(error)
    console.error('❌ loadParseResult 載入失敗:', {
      error,
      apiError,
      message: apiError.message,
      stack: error.stack
    })
    ElMessage.error(`載入解析結果失敗: ${apiError.message}`)
  }
}

const startPolling = () => {
  if (pollingTimer.value) {
    console.log('⚠️ 輪詢已經在運行，跳過啟動')
    return
  }

  console.log('🔄 啟動狀態輪詢，每2秒檢查一次')
  pollingTimer.value = setInterval(async () => {
    console.log('🔍 輪詢檢查:', {
      isProcessing: isProcessing.value,
      taskStatus: taskInfo.value?.status,
      taskId: taskInfo.value?.task_id
    })

    if (isProcessing.value) {
      console.log('📊 任務處理中，載入最新狀態...')
      await loadTaskInfo()
    } else {
      console.log('✅ 任務不在處理中，停止輪詢')
      stopPolling()
    }
  }, 2000) // 每2秒輪詢一次
}

const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
  }
}



const handleStartParse = async () => {
  if (!taskInfo.value?.file_id || !selectedParseMethod.value) {
    ElMessage.warning('缺少必要信息')
    return
  }

  isStartingParse.value = true

  try {
    const response = await parseAPI.startParse(
      taskInfo.value.file_id,
      selectedParseMethod.value
    )

    const taskId = response.data.task_id
    ElMessage.success('解析任務已開始')

    // 更新當前頁面的URL為任務ID
    await router.replace(`/results/${taskId}`)

    // 重新載入任務信息
    await loadTaskInfo()

    // 開始進度追蹤
    await startTracking(taskId)

    // 開始輪詢（保持原有邏輯作為備份）
    if (isProcessing.value) {
      startPolling()
    }

  } catch (error) {
    const apiError = handleApiError(error)
    ElMessage.error(`啟動解析失敗: ${apiError.message}`)
  } finally {
    isStartingParse.value = false
  }
}

const handleRefreshProgress = async () => {
  await refreshStatus()
}

const handleCancelTask = async () => {
  try {
    await ElMessageBox.confirm(
      '確定要取消當前解析任務嗎？',
      '取消任務',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await cancelTask()

    // 更新任務信息
    if (taskInfo.value) {
      taskInfo.value.status = 'cancelled'
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消任務失敗:', error)
    }
  }
}

const handleRetry = async () => {
  if (!taskInfo.value) return

  try {
    await ElMessageBox.confirm(
      '確定要重新解析此文件嗎？',
      '重新解析',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // TODO: 實現重新解析邏輯
    ElMessage.info('重新解析功能開發中...')
  } catch (error) {
    // 用戶取消
  }
}

const handleGoBack = () => {
  router.push('/upload')
}

const saveToKnowledge = () => {
  // TODO: 實現保存到知識庫功能
  ElMessage.info('保存到知識庫功能開發中...')
}

const startTraining = () => {
  // TODO: 實現開始訓練功能
  ElMessage.info('GraphRAG訓練功能開發中...')
}

const handleExport = (format: string) => {
  // TODO: 實現導出功能
  ElMessage.info(`導出為 ${format.toUpperCase()} 功能開發中...`)
}

// 生命週期
onMounted(async () => {
  console.log('🚀 ResultsView onMounted 開始')
  isLoading.value = true

  console.log('📋 開始載入任務信息...')
  await loadTaskInfo()
  console.log('📋 任務信息載入完成:', {
    hasTaskInfo: !!taskInfo.value,
    taskStatus: taskInfo.value?.status,
    hasParseResult: !!parseResult.value
  })

  // 如果有任務ID，啟動進度追蹤
  const taskId = route.params.taskId as string
  if (taskId) {
    console.log('🔄 開始追蹤任務:', taskId)
    await startTracking(taskId)
    console.log('🔄 任務追蹤啟動完成')
  }

  // 保持原有輪詢邏輯作為備份
  console.log('🔍 檢查是否需要啟動輪詢:', {
    isProcessing: isProcessing.value,
    taskStatus: taskInfo.value?.status,
    taskId: taskInfo.value?.task_id
  })

  if (isProcessing.value) {
    console.log('🚀 任務處理中，啟動輪詢')
    startPolling()
  } else {
    console.log('⏸️ 任務不在處理中，不啟動輪詢')
  }

  isLoading.value = false
})

onUnmounted(() => {
  stopPolling()
})
</script>
<style scoped>
.results-container {
  max-width: 80vw;
  width: 100%;
  margin: 0 auto;
  padding: 20px;

  @media (max-width: 768px) {
    max-width: 95vw;
    padding: 15px;
  }

  @media (min-width: 1920px) {
    max-width: 1600px;
  }
}


.empty-container {
  text-align: center;
  padding: 60px 20px;
}

.loading-container {
  padding: 40px 20px;
}
</style>
