<template>
    <div class="stats-content">
      <el-table :data="items">
        <el-table-column prop="title" label="審查要項" sortable></el-table-column>
        <el-table-column prop="memo" label="審查方式" sortable></el-table-column>
        <el-table-column prop="status" label="作業狀態" sortable></el-table-column>
        <el-table-column prop="result" label="處理結果" sortable>
            <template #default="scope">
                <el-button icon="InfoFilled" @click="handleShow(scope.row.result)"></el-button>                
            </template>
        </el-table-column>
      </el-table>      

      <el-dialog v-model="showInfoDialog" width="80vw">
        <div v-html="resultInfo">

        </div>
        </el-dialog>
    </div>
  </template>
  
  <script setup lang="ts">
  import axios from 'axios'
import {onMounted,ref} from 'vue'

  // Props
  interface Props {
    statistics?: any
    parseMethod?: string
    status?: string
    showDetailed?: boolean
    items?: any,
    showInfoDialog?:boolean    
  }
  
  const props = withDefaults(defineProps<Props>(), {    
    statistics: null,
    parseMethod: 'text',
    status: 'completed',
    showDetailed: true,
    items : [
        {title:"法規比對",memo:"與中心相關作業規定比對",status:"執行中",result:"/mock/previewResult1.html"},        
        {title:"陸製品限制比對",memo:"與中心相關作業規定比對",status:"等待中",result:"/mock/previewResult1.html"},        
        {title:"需求合理性(含籌補率)",memo:"生產用料以料件籌補分析表比對審查",status:"已完成",result:"/mock/previewResult1.html"},        
        {title:"料號合規性",memo:"生產用料籌補是否引用正式料號",status:"已完成",result:"/mock/previewResult1.html"},        
        {title:"預算合理性(歷史購價)",memo:"申購料件歷史單價",status:"失敗",result:"/mock/previewResult1.html"},        
        {title:"籌補期程合理性",memo:"申購料件籌補依據與協議書簽署期程是否<60天",status:"已完成",result:"/mock/previewResult1.html"},        
        {title:"檢驗技資完整性",memo:"申購料件驗收檢驗項目表是否已完備",status:"已完成",result:"/mock/previewResult1.html"},        
        {title:"預算單、總價相符",memo:"採購計畫各項單價*數量及加總後總價是否相符",status:"已完成",result:"/mock/previewResult1.html"},        
        {title:"巨額及重大採購是否依規定簽報單位主官核准",memo:"是否填報巨額及重大採購預期效益評估報告",status:"已完成",result:"/mock/previewResult1.html"},        
        {title:"保固條款",memo:"保固條款是否引用國防部內購財物採購契約通用條款第13條及國防部工程、財物暨勞務採購投標須知第34點",status:"已完成",result:"/mock/previewResult1.html"},        
        {title:"罰則:逾期罰款",memo:"每日罰款金額?罰款總額上限?解除或終止契約條件?",status:"已完成",result:"/mock/previewResult1.html"},        
        {title:"罰則:違約罰則",memo:"違約罰則要求",status:"已完成",result:"/mock/previewResult1.html"},        
        {title:"同等品要求",memo:"同等品要求為何?",status:"已完成",result:"/mock/previewResult1.html"},        
        {title:"售後服務與教育訓練",memo:"售後服務要求為何?",status:"已完成",result:"/mock/previewResult1.html"},        
        {title:"品名料號及規格報價及決標方式",memo:"是否有填註",status:"已完成",result:"/mock/previewResult1.html"},        
    ]
  })

  const resultInfo = ref("")
  const showInfoDialog = ref(false)
  
  // 方法
  const handleShow = async (showurl: string): Promise<string> => {
  let result = "";

  try {
    const response = await fetch(showurl); // make a GET request to showurl
    result = await response.text(); // convert the response to text and assign it to result
    console.log(result); // log the data to the console
  } catch (error) {
    console.error('Error:', error); // catch any errors and log them to the console
  }
  
  resultInfo.value = result   
  showInfoDialog.value = true  

  return result;
}

  const formatNumber = (num: number): string => {
    return num.toLocaleString('zh-TW')
  }
  
  const formatTime = (seconds: number): string => {
    if (!seconds || seconds <= 0) return '未知'
    
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    
    if (hours > 0) {
      return `${hours}小時${minutes}分鐘${secs}秒`
    } else if (minutes > 0) {
      return `${minutes}分鐘${secs}秒`
    } else {
      return `${secs}秒`
    }
  }
  
  const formatFileSize = (bytes: number): string => {
    if (!bytes) return '未知'
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
  }
  
  const getMethodName = (method: string): string => {
    const methodMap = {
      text: '文字解析',
      ocr: 'OCR解析',
      multimodal: 'AI多模態解析'
    }
    return methodMap[method] || method
  }
  
  const getStatusType = (status: string): string => {
    const statusMap = {
      pending: 'warning',
      processing: 'warning',
      completed: 'success',
      failed: 'danger',
      cancelled: 'info'
    }
    return statusMap[status] || 'info'
  }
  
  const getStatusText = (status: string): string => {
    const statusMap = {
      pending: '等待中',
      processing: '處理中',
      completed: '已完成',
      failed: '失敗',
      cancelled: '已取消'
    }
    return statusMap[status] || status
  }
  </script>
  
  <style scoped>
  .stats-content {
    padding: 20px 0;
  }
  
  .stat-card {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
  }
  
  .stat-card:hover {
    background: #e9ecef;
    transform: translateY(-2px);
  }
  
  .detailed-stats {
    margin-top: 30px;
  }
  
  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    margin-bottom: 10px;
  }
  
  .stat-label {
    font-weight: 500;
    color: #606266;
  }
  
  .stat-value {
    font-weight: 600;
    color: #303133;
  }
  
  /* 響應式設計 */
  @media (max-width: 768px) {
    .stats-content .el-col {
      margin-bottom: 15px;
    }
    
    .stat-item {
      flex-direction: column;
      gap: 5px;
      text-align: center;
    }
  }
  </style>
  